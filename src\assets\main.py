import flet as ft
import math
import random
import time
import asyncio
from typing import List, Callable, Optional
from flet.canvas import 


class Ruleta:
    def __init__(self, opciones: List[str], on_result: Optional[Callable[[str], None]] = None):
        """
        Clase Ruleta para crear una ruleta interactiva con canvas
        
        Args:
            opciones: Lista de opciones/textos para la ruleta
            on_result: Callback que se ejecuta cuando la ruleta se detiene con el resultado
        """
        self.opciones = opciones
        self.on_result = on_result
        self.num_opciones = len(opciones)
        self.angulo_actual = 0.0
        self.velocidad = 0.0
        self.girando = False
        self.canvas_size = 400
        self.radio = 150
        self.centro_x = self.canvas_size // 2
        self.centro_y = self.canvas_size // 2
        
        # Colores para las secciones
        self.colores = [
            ft.colors.RED_400, ft.colors.BLUE_400, ft.colors.GREEN_400, ft.colors.YELLOW_400,
            ft.colors.PURPLE_400, ft.colors.ORANGE_400, ft.colors.TEAL_400, ft.colors.PINK_400,
            ft.colors.INDIGO_400, ft.colors.CYAN_400, ft.colors.LIME_400, ft.colors.AMBER_400
        ]
        
        self._crear_canvas()
    
    def _crear_canvas(self):
        """Crea el canvas y sus elementos"""
        self.canvas = ft.Canvas(
            width=self.canvas_size,
            height=self.canvas_size,
            shapes=[],
        )
        self._dibujar_ruleta()
    
    def _dibujar_ruleta(self):
        """Dibuja la ruleta completa"""
        self.canvas.shapes.clear()
        
        # Dibujar las secciones de la ruleta
        angulo_por_seccion = 360 / self.num_opciones  # En grados
        
        for i in range(self.num_opciones):
            angulo_inicio = i * angulo_por_seccion + (self.angulo_actual * 180 / math.pi)
            color = self.colores[i % len(self.colores)]
            
            # Crear sector circular usando Arc
            self._dibujar_sector(angulo_inicio, angulo_por_seccion, color, i)
        
        # Dibujar la flecha indicadora (fija en la parte superior)
        self._dibujar_flecha()
        
        # Dibujar círculo central
        self._dibujar_centro()
    
    def _dibujar_sector(self, angulo_inicio: float, angulo_sector: float, color: str, indice: int):
        """Dibuja un sector de la ruleta usando Arc"""
        # Crear arco para el sector
        arco = ft.canvas.Arc(
            x=self.centro_x - self.radio,
            y=self.centro_y - self.radio,
            width=self.radio * 2,
            height=self.radio * 2,
            start_angle=math.radians(angulo_inicio),
            sweep_angle=math.radians(angulo_sector),
            use_center=True,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=color,
            ),
        )
        self.canvas.shapes.append(arco)
        
        # Borde del sector
        borde = ft.canvas.Arc(
            x=self.centro_x - self.radio,
            y=self.centro_y - self.radio,
            width=self.radio * 2,
            height=self.radio * 2,
            start_angle=math.radians(angulo_inicio),
            sweep_angle=math.radians(angulo_sector),
            use_center=True,
            paint=ft.Paint(
                style=ft.PaintingStyle.STROKE,
                color=ft.colors.WHITE,
                stroke_width=3,
            ),
        )
        self.canvas.shapes.append(borde)
        
        # Dibujar texto de la opción
        self._dibujar_texto_opcion(indice, angulo_inicio, angulo_sector)
    
    def _dibujar_texto_opcion(self, indice: int, angulo_inicio: float, angulo_sector: float):
        """Dibuja el texto de una opción en su sector"""
        angulo_medio_rad = math.radians(angulo_inicio + angulo_sector / 2)
        radio_texto = self.radio * 0.65
        
        x = self.centro_x + radio_texto * math.cos(angulo_medio_rad)
        y = self.centro_y + radio_texto * math.sin(angulo_medio_rad)
        
        # Ajustar posición del texto para centrarlo mejor
        texto = ft.canvas.Text(
            x=x - 30,  # Ajuste para centrar
            y=y - 8,   # Ajuste para centrar
            text=self.opciones[indice],
            style=ft.TextStyle(
                size=14,
                weight=ft.FontWeight.BOLD,
                color=ft.colors.WHITE,
            ),
        )
        self.canvas.shapes.append(texto)
    
    def _dibujar_flecha(self):
        """Dibuja la flecha indicadora en la parte superior"""
        # Triángulo apuntando hacia abajo
        x1, y1 = self.centro_x, self.centro_y - self.radio - 30
        x2, y2 = self.centro_x - 20, self.centro_y - self.radio - 10
        x3, y3 = self.centro_x + 20, self.centro_y - self.radio - 10
        
        path_data = f"M {x1} {y1} L {x2} {y2} L {x3} {y3} Z"
        
        flecha = ft.canvas.Path(
            path_data,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=ft.colors.RED_700,
            ),
        )
        self.canvas.shapes.append(flecha)
    
    def _dibujar_centro(self):
        """Dibuja el círculo central"""
        centro = ft.canvas.Circle(
            x=self.centro_x,
            y=self.centro_y,
            radius=25,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=ft.colors.GREY_800,
            ),
        )
        self.canvas.shapes.append(centro)
    
    async def play(self):
        """Inicia la animación de giro de la ruleta"""
        if self.girando:
            return
        
        self.girando = True
        
        # Velocidad inicial aleatoria
        self.velocidad = random.uniform(0.3, 0.6)
        
        # Duración del giro (3-6 segundos)
        duracion = random.uniform(3.0, 6.0)
        tiempo_inicio = time.time()
        
        while self.girando and (time.time() - tiempo_inicio) < duracion:
            # Actualizar ángulo
            self.angulo_actual += self.velocidad
            
            # Reducir velocidad gradualmente
            factor_reduccion = 1 - (time.time() - tiempo_inicio) / duracion
            self.velocidad *= (0.98 + factor_reduccion * 0.01)
            
            # Redibujar ruleta
            self._dibujar_ruleta()
            self.canvas.update()
            
            # Pequeña pausa para la animación
            await asyncio.sleep(0.05)
        
        self.girando = False
        
        # Calcular resultado final
        resultado = self._calcular_resultado()
        
        # Ejecutar callback si existe
        if self.on_result:
            self.on_result(resultado)
    
    def _calcular_resultado(self) -> str:
        """Calcula qué opción fue seleccionada"""
        # Normalizar el ángulo
        angulo_normalizado = self.angulo_actual % (2 * math.pi)
        
        # La flecha apunta hacia arriba (270 grados o 3π/2)
        # Ajustar para que apunte hacia la sección correcta
        angulo_flecha = (3 * math.pi / 2 - angulo_normalizado) % (2 * math.pi)
        
        # Calcular qué sección
        angulo_por_seccion = 2 * math.pi / self.num_opciones
        indice_seccion = int(angulo_flecha / angulo_por_seccion)
        
        return self.opciones[indice_seccion]
    
    def get_canvas(self) -> ft.Canvas:
        """Retorna el canvas para agregarlo a la página"""
        return self.canvas


def main(page: ft.Page):
    page.title = "Ruleta Interactiva"
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.bgcolor = ft.colors.BLUE_GREY_900
    
    # Opciones de la ruleta
    opciones_ruleta = [
        "Premio 1", "Premio 2", "Premio 3", "Premio 4",
        "Premio 5", "Premio 6", "Intenta de nuevo", "Gran Premio"
    ]
    
    # Texto para mostrar el resultado
    resultado_text = ft.Text(
        "¡Presiona GIRAR para jugar!",
        size=20,
        weight=ft.FontWeight.BOLD,
        text_align=ft.TextAlign.CENTER,
        color=ft.colors.WHITE
    )
    
    def on_resultado(resultado: str):
        """Callback que se ejecuta cuando la ruleta se detiene"""
        resultado_text.value = f"🎉 ¡Ganaste: {resultado}! 🎉"
        resultado_text.update()
    
    # Crear la ruleta
    ruleta = Ruleta(opciones_ruleta, on_resultado)
    
    async def girar_ruleta(e):
        """Handler para el botón de girar"""
        if not ruleta.girando:
            resultado_text.value = "🎯 Girando..."
            resultado_text.update()
            await ruleta.play()
    
    # Botón para girar
    boton_girar = ft.ElevatedButton(
        "🎲 GIRAR RULETA",
        on_click=girar_ruleta,
        style=ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD),
        ),
        width=200,
        height=50,
    )
    
    # Layout principal
    page.add(
        ft.Column(
            [
                ft.Text(
                    "🎡 RULETA DE LA FORTUNA 🎡",
                    size=30,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                    color=ft.colors.WHITE,
                ),
                ft.Container(height=20),  # Espaciado
                ft.Container(
                    content=ruleta.get_canvas(),
                    bgcolor=ft.colors.WHITE,
                    border_radius=10,
                    padding=10,
                ),
                ft.Container(height=20),  # Espaciado
                boton_girar,
                ft.Container(height=10),  # Espaciado
                resultado_text,
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        )
    )


ft.app(main, view=ft.AppView.WEB_BROWSER)
