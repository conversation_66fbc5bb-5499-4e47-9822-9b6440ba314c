import flet as ft
import math
import random
import time
import asyncio
from typing import List, Callable, Optional
from flet.canvas import Arc, Canvas, CanvasResizeEvent, Circle, Color, Fill, Line, Oval, Path, PointMode, Points, Rect, Shadow, Text


class Ruleta(ft.Container):
    def __init__(self, opciones: List[str], on_result: Optional[Callable[[str], None]] = None):
        """
        Clase Ruleta para crear una ruleta interactiva con canvas

        Args:
            opciones: Lista de opciones/textos para la ruleta
            on_result: Callback que se ejecuta cuando la ruleta se detiene con el resultado
        """
        # Validar entrada
        if not opciones or len(opciones) == 0:
            raise ValueError("La lista de opciones no puede estar vacía")

        self.opciones = opciones
        self.on_result = on_result
        self.num_opciones = len(opciones)
        self.angulo_actual = 0.0
        self.velocidad = 0.0
        self.girando = False
        self.canvas_size = 400
        self.radio = 150
        self.centro_x = self.canvas_size // 2
        self.centro_y = self.canvas_size // 2

        # Colores para las secciones
        self.colores = [
            ft.Colors.RED_400, ft.Colors.BLUE_400, ft.Colors.GREEN_400, ft.Colors.YELLOW_400,
            ft.Colors.PURPLE_400, ft.Colors.ORANGE_400, ft.Colors.TEAL_400, ft.Colors.PINK_400,
            ft.Colors.INDIGO_400, ft.Colors.CYAN_400, ft.Colors.LIME_400, ft.Colors.AMBER_400
        ]

        # Crear canvas antes de llamar super().__init__()
        self._crear_canvas()

        # Llamar al constructor padre con el canvas como contenido
        super().__init__(
            content=self.canvas,
            width=self.canvas_size + 20,
            height=self.canvas_size + 20,
            bgcolor=ft.Colors.WHITE,
            border_radius=10,
            padding=10,
            alignment=ft.alignment.center
        )
    
    def _crear_canvas(self):
        """Crea el canvas y sus elementos"""
        self.canvas = Canvas(
            width=self.canvas_size,
            height=self.canvas_size,
            shapes=[],
            # on_tap=self._on_canvas_click,
        )
        self._dibujar_ruleta()

    def _on_canvas_click(self, _: ft.TapEvent):
        """Maneja el clic en el canvas para iniciar el giro"""
        if not self.girando:
            asyncio.create_task(self.play())
    
    def _dibujar_ruleta(self):
        """Dibuja la ruleta completa"""
        self.canvas.shapes.clear()
        
        # Dibujar las secciones de la ruleta
        angulo_por_seccion = 360 / self.num_opciones  # En grados
        
        for i in range(self.num_opciones):
            angulo_inicio = i * angulo_por_seccion + (self.angulo_actual * 180 / math.pi)
            color = self.colores[i % len(self.colores)]
            
            # Crear sector circular usando Arc
            self._dibujar_sector(angulo_inicio, angulo_por_seccion, color, i)
        
        # Dibujar la flecha indicadora (fija en la parte superior)
        self._dibujar_flecha()
        
        # Dibujar círculo central
        self._dibujar_centro()
    
    def _dibujar_sector(self, angulo_inicio: float, angulo_sector: float, color: str, indice: int):
        """Dibuja un sector de la ruleta usando Arc"""
        # Crear arco para el sector
        arco = Arc(
            x=self.centro_x - self.radio,
            y=self.centro_y - self.radio,
            width=self.radio * 2,
            height=self.radio * 2,
            start_angle=math.radians(angulo_inicio),
            sweep_angle=math.radians(angulo_sector),
            use_center=True,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=color,
            ),
        )
        self.canvas.shapes.append(arco)
        
        # Borde del sector
        borde = Arc(
            x=self.centro_x - self.radio,
            y=self.centro_y - self.radio,
            width=self.radio * 2,
            height=self.radio * 2,
            start_angle=math.radians(angulo_inicio),
            sweep_angle=math.radians(angulo_sector),
            use_center=True,
            paint=ft.Paint(
                style=ft.PaintingStyle.STROKE,
                color=ft.Colors.WHITE,
                stroke_width=3,
            ),
        )
        self.canvas.shapes.append(borde)
        
        # Dibujar texto de la opción
        self._dibujar_texto_opcion(indice, angulo_inicio, angulo_sector)
    
    def _dibujar_texto_opcion(self, indice: int, angulo_inicio: float, angulo_sector: float):
        """Dibuja el texto de una opción en su sector"""
        angulo_medio_rad = math.radians(angulo_inicio + angulo_sector / 2)
        radio_texto = self.radio * 0.7

        x = self.centro_x + radio_texto * math.cos(angulo_medio_rad)
        y = self.centro_y + radio_texto * math.sin(angulo_medio_rad)

        # Calcular el tamaño del texto basado en la longitud
        texto_opcion = self.opciones[indice]
        tamaño_texto = min(16, max(10, 120 // len(texto_opcion)))

        # Ajustar posición del texto para centrarlo mejor
        offset_x = len(texto_opcion) * tamaño_texto * 0.3
        offset_y = tamaño_texto * 0.4

        texto = Text(
            x=x - offset_x,
            y=y - offset_y,
            text=texto_opcion,
            style=ft.TextStyle(
                size=tamaño_texto,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
            ),
        )
        self.canvas.shapes.append(texto)
    
    def _dibujar_flecha(self):
        """Dibuja la flecha indicadora en la parte superior"""
        # Triángulo apuntando hacia abajo
        x1, y1 = self.centro_x, self.centro_y - self.radio - 30
        x2, y2 = self.centro_x - 20, self.centro_y - self.radio - 10
        x3, y3 = self.centro_x + 20, self.centro_y - self.radio - 10
        
        path_data = f"M {x1} {y1} L {x2} {y2} L {x3} {y3} Z"
        
        flecha = Path(
            path_data,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=ft.Colors.RED_700,
            ),
        )
        self.canvas.shapes.append(flecha)
    
    def _dibujar_centro(self):
        """Dibuja el círculo central"""
        centro = Circle(
            x=self.centro_x,
            y=self.centro_y,
            radius=25,
            paint=ft.Paint(
                style=ft.PaintingStyle.FILL,
                color=ft.Colors.GREY_800,
            ),
        )
        self.canvas.shapes.append(centro)
    
    async def play(self):
        """Inicia la animación de giro de la ruleta"""
        if self.girando:
            return

        try:
            self.girando = True

            # Velocidad inicial aleatoria (más realista)
            velocidad_inicial = random.uniform(0.4, 0.8)
            self.velocidad = velocidad_inicial

            # Duración del giro (4-7 segundos)
            duracion = random.uniform(4.0, 7.0)
            tiempo_inicio = time.time()

            # Variables para animación suave
            frame_rate = 60  # FPS objetivo
            frame_time = 1.0 / frame_rate

            while self.girando:
                tiempo_actual = time.time()
                tiempo_transcurrido = tiempo_actual - tiempo_inicio

                # Verificar si debe terminar
                if tiempo_transcurrido >= duracion:
                    break

                # Calcular factor de desaceleración usando curva exponencial
                progreso = tiempo_transcurrido / duracion
                factor_desaceleracion = math.exp(-3 * progreso)  # Desaceleración exponencial

                # Actualizar velocidad
                self.velocidad = velocidad_inicial * factor_desaceleracion

                # Actualizar ángulo
                self.angulo_actual += self.velocidad * frame_time

                # Redibujar ruleta
                self._dibujar_ruleta()

                # Actualizar canvas si está disponible
                if hasattr(self.canvas, 'update'):
                    self.update()

                # Pausa para mantener frame rate
                await asyncio.sleep(frame_time)

            self.girando = False

            # Calcular resultado final
            resultado = self._calcular_resultado()

            # Ejecutar callback si existe
            if self.on_result:
                try:
                    self.on_result(resultado)
                except Exception as e:
                    print(f"Error en callback on_result: {e}")

        except Exception as e:
            print(f"Error durante la animación: {e}")
            self.girando = False
    
    def _calcular_resultado(self) -> str:
        """Calcula qué opción fue seleccionada"""
        try:
            # Normalizar el ángulo
            angulo_normalizado = self.angulo_actual % (2 * math.pi)

            # La flecha apunta hacia arriba (270 grados o 3π/2)
            # Ajustar para que apunte hacia la sección correcta
            angulo_flecha = (3 * math.pi / 2 - angulo_normalizado) % (2 * math.pi)

            # Calcular qué sección
            angulo_por_seccion = 2 * math.pi / self.num_opciones
            indice_seccion = int(angulo_flecha / angulo_por_seccion)

            # Asegurar que el índice esté en rango válido
            indice_seccion = max(0, min(indice_seccion, self.num_opciones - 1))

            return self.opciones[indice_seccion]
        except Exception as e:
            print(f"Error calculando resultado: {e}")
            return self.opciones[0]  # Retornar primera opción como fallback

    def get_canvas(self) -> Canvas:
        """Retorna el canvas para agregarlo a la página"""
        return self.canvas

    def set_options(self, nuevas_opciones: List[str]):
        """Permite cambiar las opciones de la ruleta"""
        if not nuevas_opciones or len(nuevas_opciones) == 0:
            raise ValueError("La lista de opciones no puede estar vacía")

        self.opciones = nuevas_opciones
        self.num_opciones = len(nuevas_opciones)
        self._dibujar_ruleta()
        if hasattr(self.canvas, 'update'):
            self.update()

    def is_spinning(self) -> bool:
        """Retorna True si la ruleta está girando"""
        return self.girando

    def stop(self):
        """Detiene la ruleta inmediatamente"""
        self.girando = False


def main(page: ft.Page):
    page.title = "Ruleta Interactiva"
    page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
    page.vertical_alignment = ft.MainAxisAlignment.CENTER
    page.bgcolor = ft.Colors.BLUE_GREY_900
    
    # Opciones de la ruleta
    opciones_ruleta = [
        "Premio 1", "Premio 2", "Premio 3", "Premio 4",
        "Premio 5", "Premio 6", "Intenta de nuevo", "Gran Premio"
    ]
    
    # Texto para mostrar el resultado
    resultado_text = ft.Text(
        "¡Presiona GIRAR para jugar!",
        size=20,
        weight=ft.FontWeight.BOLD,
        text_align=ft.TextAlign.CENTER,
        color=ft.Colors.WHITE
    )
    
    def on_resultado(resultado: str):
        """Callback que se ejecuta cuando la ruleta se detiene"""
        resultado_text.value = f"🎉 ¡Ganaste: {resultado}! 🎉"
        resultado_text.update()
    
    # Crear la ruleta
    ruleta = Ruleta(opciones_ruleta, on_resultado)
    
    async def girar_ruleta(_):
        """Handler para el botón de girar"""
        if not ruleta.girando:
            resultado_text.value = "🎯 Girando..."
            resultado_text.update()
            await ruleta.play()

    # Botón para girar
    boton_girar = ft.ElevatedButton(
        "🎲 GIRAR RULETA",
        on_click=girar_ruleta,
        style=ft.ButtonStyle(
            color=ft.Colors.WHITE,
            bgcolor=ft.Colors.BLUE,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.BOLD),
        ),
        width=200,
        height=50,
    )

    # Información adicional
    info_text = ft.Text(
        "💡 Haz clic en la ruleta o presiona el botón para girar",
        size=12,
        text_align=ft.TextAlign.CENTER,
        color=ft.Colors.WHITE70,
        italic=True,
    )

    # Layout principal
    page.add(
        ft.Column(
            [
                ft.Text(
                    "🎡 RULETA DE LA FORTUNA 🎡",
                    size=30,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                    color=ft.Colors.WHITE,
                ),
                ft.Container(height=20),  # Espaciado
                ruleta,  # Usar directamente la ruleta como Container
                ft.Container(height=20),  # Espaciado
                boton_girar,
                ft.Container(height=10),  # Espaciado
                resultado_text,
                ft.Container(height=10),  # Espaciado
                info_text,
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        )
    )


ft.app(main, view=ft.AppView.WEB_BROWSER)
